#!/usr/bin/env python3
"""
测试ChestnutCMS文章同步功能
从chestnut_cms数据库获取两篇文章，转化为txt格式并上传到RAG应用
"""
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from backend.app.services.mysql_service import MySQLService
from backend.app.services.chestnut_cms_service import ChestnutCMSService
from backend.app.services.rag_service import RAGService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_mysql_connection():
    """测试MySQL连接"""
    print("=" * 60)
    print("1. 测试MySQL连接")
    print("=" * 60)
    
    mysql_service = MySQLService()
    result = mysql_service.test_connection()
    
    if result["success"]:
        print("✅ MySQL连接成功!")
        conn_info = result["connection_info"]
        print(f"   数据库: {conn_info['current_database']}")
        print(f"   MySQL版本: {conn_info['mysql_version']}")
        print(f"   表数量: {conn_info['tables_count']}")
        return True
    else:
        print("❌ MySQL连接失败!")
        print(f"   错误: {result['message']}")
        return False


def test_get_cms_articles():
    """测试获取CMS文章"""
    print("\n" + "=" * 60)
    print("2. 测试获取CMS文章")
    print("=" * 60)
    
    mysql_service = MySQLService()
    
    try:
        # 获取文章统计信息
        summary = mysql_service.get_chestnut_cms_articles_summary()
        print(f"📊 文章统计:")
        print(f"   总文章数: {summary.get('total_articles', 0)}")
        print(f"   有内容的文章数: {summary.get('articles_with_content', 0)}")
        print(f"   最新文章日期: {summary.get('latest_publish_date', '未知')}")
        
        # 获取前两篇文章
        articles = mysql_service.get_chestnut_cms_articles()
        
        if not articles:
            print("❌ 未找到任何文章")
            return []
        
        # 只取前两篇用于测试
        test_articles = articles[:2]
        
        print(f"\n📄 获取到 {len(articles)} 篇文章，选择前 {len(test_articles)} 篇进行测试:")
        
        for i, article in enumerate(test_articles, 1):
            print(f"\n   文章 {i}:")
            print(f"     ID: {article['content_id']}")
            print(f"     标题: {article['title']}")
            print(f"     发布日期: {article['publish_date']}")
            print(f"     分类路径: {article.get('path', '无')}")
            print(f"     内容长度: {len(article.get('content_html', '')) if article.get('content_html') else 0} 字符")
        
        return test_articles
        
    except Exception as e:
        print(f"❌ 获取CMS文章失败: {e}")
        return []


def test_html_cleaning(articles):
    """测试HTML内容清理"""
    print("\n" + "=" * 60)
    print("3. 测试HTML内容清理")
    print("=" * 60)
    
    cms_service = ChestnutCMSService()
    cleaned_articles = []
    
    for i, article in enumerate(articles, 1):
        print(f"\n📝 处理文章 {i}: {article['title']}")
        
        html_content = article.get('content_html', '')
        if not html_content:
            print("   ⚠️  文章内容为空，跳过")
            continue
        
        # 清理HTML内容
        clean_text = cms_service.clean_html_content(html_content)
        
        print(f"   原始HTML长度: {len(html_content)} 字符")
        print(f"   清理后文本长度: {len(clean_text)} 字符")
        print(f"   清理后前200字符: {clean_text[:200]}...")
        
        # 构建文章URL
        path = article.get('path', '')
        file_url = cms_service.build_article_url(str(article['content_id']), path)
        print(f"   文章URL: {file_url}")
        
        cleaned_articles.append({
            'article': article,
            'clean_text': clean_text,
            'file_url': file_url
        })
    
    return cleaned_articles


def test_rag_upload(cleaned_articles):
    """测试上传到RAG系统"""
    print("\n" + "=" * 60)
    print("4. 测试上传到RAG系统")
    print("=" * 60)
    
    rag_service = RAGService()
    upload_results = []
    
    # 先检查RAG系统状态
    status = rag_service.get_status()
    print(f"📊 RAG系统状态:")
    print(f"   状态: {status.get('status', '未知')}")
    print(f"   文档数量: {status.get('documents_count', 0)}")
    print(f"   存储大小: {status.get('storage_size', '未知')}")
    
    for i, item in enumerate(cleaned_articles, 1):
        article = item['article']
        clean_text = item['clean_text']
        file_url = item['file_url']
        
        content_id = str(article['content_id'])
        title = article['title'] or f"文章{content_id}"
        filename = f"{content_id}.txt"
        
        print(f"\n📤 上传文章 {i}: {title}")
        print(f"   文件名: {filename}")
        
        # 创建文档内容（包含标题和正文）
        document_content = f"标题: {title}\n\n{clean_text}"
        
        # 准备扩展元数据
        additional_metadata = {
            "content_id": content_id,
            "title": title,
            "file_url": file_url,
            "publish_date": str(article["publish_date"]) if article["publish_date"] else None,
            "source": "chestnut_cms"
        }
        
        # 上传文档
        result = rag_service.upload_document(document_content, filename, additional_metadata)
        
        if result["success"]:
            print(f"   ✅ 上传成功!")
            print(f"      文件路径: {result.get('file_path', '未知')}")
            print(f"      文档块数: {result.get('new_chunks', 0)}")
            print(f"      是否替换: {'是' if result.get('replaced', False) else '否'}")
        else:
            print(f"   ❌ 上传失败: {result['message']}")
        
        upload_results.append(result)
    
    return upload_results


def test_rag_query(test_articles):
    """测试RAG查询功能"""
    print("\n" + "=" * 60)
    print("5. 测试RAG查询功能")
    print("=" * 60)
    
    rag_service = RAGService()
    
    # 使用第一篇文章的标题作为查询
    if test_articles:
        first_article = test_articles[0]
        query = first_article['title'][:20]  # 取标题前20个字符作为查询
        
        print(f"🔍 查询问题: {query}")
        
        result = rag_service.query(query, max_results=3)
        
        if result["success"]:
            print(f"✅ 查询成功!")
            print(f"   回答: {result['answer'][:200]}...")
            print(f"   相关文档数: {result['total_sources']}")
            
            for i, source in enumerate(result['sources'], 1):
                metadata = source['metadata']
                print(f"\n   📄 相关文档 {i}:")
                print(f"      文件名: {metadata.get('filename', '未知')}")
                print(f"      标题: {metadata.get('title', '未知')}")
                print(f"      文章ID: {metadata.get('content_id', '未知')}")
                print(f"      文章链接: {metadata.get('file_url', '未知')}")
                print(f"      相关度分数: {source.get('score', 0.0):.3f}")
                print(f"      内容片段: {source['content'][:100]}...")
        else:
            print(f"❌ 查询失败: {result['message']}")
    else:
        print("⚠️  没有测试文章，跳过查询测试")


def test_documents_list():
    """测试获取文档列表"""
    print("\n" + "=" * 60)
    print("6. 测试获取文档列表")
    print("=" * 60)
    
    rag_service = RAGService()
    result = rag_service.get_documents_list()
    
    if result["success"]:
        documents = result["documents"]
        print(f"📋 文档列表 (共 {len(documents)} 个文档):")
        
        for i, doc in enumerate(documents, 1):
            print(f"\n   文档 {i}:")
            print(f"     文件名: {doc['filename']}")
            print(f"     块数: {doc['chunks_count']}")
            print(f"     文件大小: {doc.get('file_size', 0)} 字节")
            
            # 如果是ChestnutCMS文档，显示扩展信息
            if doc.get('source') == 'chestnut_cms':
                print(f"     文章ID: {doc.get('content_id', '未知')}")
                print(f"     标题: {doc.get('title', '未知')}")
                print(f"     发布日期: {doc.get('publish_date', '未知')}")
                print(f"     文章链接: {doc.get('file_url', '未知')}")
    else:
        print(f"❌ 获取文档列表失败: {result['message']}")


def main():
    """主测试函数"""
    print("🚀 开始测试ChestnutCMS文章同步功能")
    print("=" * 60)
    
    # 1. 测试MySQL连接
    if not test_mysql_connection():
        print("\n❌ MySQL连接失败，无法继续测试")
        return
    
    # 2. 测试获取CMS文章
    test_articles = test_get_cms_articles()
    if not test_articles:
        print("\n❌ 未获取到测试文章，无法继续测试")
        return
    
    # 3. 测试HTML内容清理
    cleaned_articles = test_html_cleaning(test_articles)
    if not cleaned_articles:
        print("\n❌ HTML清理失败，无法继续测试")
        return
    
    # 4. 测试上传到RAG系统
    upload_results = test_rag_upload(cleaned_articles)
    
    # 5. 测试RAG查询功能
    test_rag_query(test_articles)
    
    # 6. 测试获取文档列表
    test_documents_list()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    successful_uploads = sum(1 for result in upload_results if result["success"])
    total_uploads = len(upload_results)
    
    print(f"✅ 成功上传文章: {successful_uploads}/{total_uploads}")
    
    if successful_uploads == total_uploads:
        print("🎉 所有测试通过！ChestnutCMS文章同步功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
