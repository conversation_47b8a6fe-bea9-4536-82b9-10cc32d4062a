"""
中间件配置
"""
import time
import logging
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

from backend.config.settings import settings

logger = logging.getLogger(__name__)


def add_middlewares(app: FastAPI) -> None:
    """添加中间件"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """请求日志中间件"""
        start_time = time.time()
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        logger.info(
            f"{request.method} {request.url.path} - "
            f"Status: {response.status_code} - "
            f"Time: {process_time:.3f}s"
        )
        
        return response
